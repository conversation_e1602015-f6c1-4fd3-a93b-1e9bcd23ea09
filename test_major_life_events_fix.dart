import 'package:flutter/material.dart';
import 'lib/features/astrology/services/major_life_events_detector.dart';
import 'lib/data/models/astrology/astro_event.dart';

/// 測試 MajorLifeEventsDetector 修正
void main() async {
  print('測試 MajorLifeEventsDetector 修正...');
  
  // 創建模擬的行星數據
  final mockNatalPlanets = [
    MockPlanet('Sun', 120.0, 6),
    MockPlanet('Moon', 180.0, 8),
  ];
  
  final mockTransitPlanets = [
    MockPlanet('Mars', 125.0, 0),
    MockPlanet('Saturn', 185.0, 0),
  ];
  
  final testDate = DateTime.now();
  
  try {
    // 測試健康事件偵測
    print('測試健康事件偵測...');
    final healthEvents = await MajorLifeEventsDetector.detectHealthEvents(
      mockNatalPlanets,
      mockTransitPlanets,
      testDate,
    );
    print('健康事件數量: ${healthEvents.length}');
    
    // 測試教育事件偵測
    print('測試教育事件偵測...');
    final educationEvents = await MajorLifeEventsDetector.detectEducationEvents(
      mockNatalPlanets,
      mockTransitPlanets,
      testDate,
    );
    print('教育事件數量: ${educationEvents.length}');
    
    // 測試搬遷事件偵測
    print('測試搬遷事件偵測...');
    final relocationEvents = await MajorLifeEventsDetector.detectRelocationEvents(
      mockNatalPlanets,
      mockTransitPlanets,
      testDate,
    );
    print('搬遷事件數量: ${relocationEvents.length}');
    
    // 測試心靈事件偵測
    print('測試心靈事件偵測...');
    final spiritualEvents = await MajorLifeEventsDetector.detectSpiritualEvents(
      mockNatalPlanets,
      mockTransitPlanets,
      testDate,
    );
    print('心靈事件數量: ${spiritualEvents.length}');
    
    print('所有測試完成！');
    
  } catch (e) {
    print('測試失敗: $e');
  }
}

/// 模擬行星類別
class MockPlanet {
  final String name;
  final double longitude;
  final int house;
  
  MockPlanet(this.name, this.longitude, this.house);
}
